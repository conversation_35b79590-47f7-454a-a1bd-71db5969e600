#!/usr/bin/env python3
"""
Webové rozhranie pre konfigur<PERSON>ciu AirCursor Assistant

<PERSON><PERSON><PERSON>je webový server pre vzdialenú konfiguráciu a monitoring.
"""

import json
import os
import threading
import webbrowser
from pathlib import Path
from typing import Dict, Any, Optional
import logging
import time # Pridaný import pre uptime simuláciu
import datetime # Pridaný import pre uptime simuláciu

try:
    from flask import Flask, render_template_string, request, jsonify, send_from_directory
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    Flask = None

logger = logging.getLogger(__name__)

class WebInterface:
    """Webové rozhranie pre AirCursor Assistant."""

    def __init__(self, config_manager=None, plugin_manager=None,
                 custom_commands_manager=None, port: int = 8080):
        if not FLASK_AVAILABLE:
            raise ImportError("Flask nie je nain<PERSON>ný. Nainštalujte: pip install flask")

        self.app = Flask(__name__)
        self.config_manager = config_manager
        self.plugin_manager = plugin_manager
        self.custom_commands_manager = custom_commands_manager
        self.port = port
        self.server_thread = None
        self.running = False
        self.start_time = time.time() # Uložíme čas spustenia

        self._setup_routes()

    def _setup_routes(self):
        """Nastaví webové routes."""

        @self.app.route('/')
        def index():
            """Hlavná stránka."""
            return render_template_string(self._get_index_template())

        @self.app.route('/api/config', methods=['GET'])
        def get_config():
            """Vráti aktuálnu konfiguráciu."""
            if not self.config_manager:
                return jsonify({'error': 'Config manager nie je dostupný'}), 500

            try:
                config = {}
                # Získaj všetky sekcie z config managera
                sections = self.config_manager.get_all_sections()
                for section in sections:
                     config[section] = self.config_manager.get(section, default={})

                return jsonify(config)
            except Exception as e:
                logger.error(f"Chyba pri načítaní konfigurácie cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/config', methods=['POST'])
        def update_config():
            """Aktualizuje konfiguráciu."""
            if not self.config_manager:
                return jsonify({'error': 'Config manager nie je dostupný'}), 500

            try:
                data = request.get_json()
                if not isinstance(data, dict):
                     return jsonify({'error': 'Neplatný formát dát'}), 400

                for section, values in data.items():
                    if isinstance(values, dict):
                        for key, value in values.items():
                            self.config_manager.set(section, key, value)
                    else:
                         logger.warning(f"Ignorujem neplatné dáta pre sekciu {section}: {values}")


                self.config_manager.save()
                logger.info("Konfigurácia úspešne aktualizovaná cez webové rozhranie.")
                return jsonify({'success': True, 'message': 'Konfigurácia úspešne aktualizovaná'})

            except Exception as e:
                logger.error(f"Chyba pri aktualizácii konfigurácie cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/plugins', methods=['GET'])
        def get_plugins():
            """Vráti zoznam pluginov."""
            if not self.plugin_manager:
                return jsonify({'error': 'Plugin manager nie je dostupný'}), 500

            try:
                plugins = []
                for plugin in self.plugin_manager.list_plugins():
                    plugins.append({
                        'name': plugin.name,
                        'version': plugin.version,
                        'description': plugin.description,
                        'author': plugin.author,
                        'enabled': plugin.enabled,
                        'loaded': plugin.loaded # Pridaný status načítania
                    })

                return jsonify(plugins)
            except Exception as e:
                logger.error(f"Chyba pri načítaní pluginov cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/plugins/<plugin_name>/enable', methods=['POST'])
        def enable_plugin(plugin_name):
            """Povolí plugin."""
            if not self.plugin_manager:
                return jsonify({'error': 'Plugin manager nie je dostupný'}), 500

            try:
                success = self.plugin_manager.enable_plugin(plugin_name)
                if success:
                    logger.info(f"Plugin '{plugin_name}' povolený cez webové rozhranie.")
                    return jsonify({'success': True, 'message': f"Plugin '{plugin_name}' bol povolený."})
                else:
                     logger.warning(f"Nepodarilo sa povoliť plugin '{plugin_name}' cez webové rozhranie.")
                     return jsonify({'success': False, 'message': f"Nepodarilo sa povoliť plugin '{plugin_name}'."}), 400
            except Exception as e:
                logger.error(f"Chyba pri povolení pluginu '{plugin_name}' cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/plugins/<plugin_name>/disable', methods=['POST'])
        def disable_plugin(plugin_name):
            """Zakáže plugin."""
            if not self.plugin_manager:
                return jsonify({'error': 'Plugin manager nie je dostupný'}), 500

            try:
                success = self.plugin_manager.disable_plugin(plugin_name)
                if success:
                    logger.info(f"Plugin '{plugin_name}' zakázaný cez webové rozhranie.")
                    return jsonify({'success': True, 'message': f"Plugin '{plugin_name}' bol zakázaný."})
                else:
                    logger.warning(f"Nepodarilo sa zakázať plugin '{plugin_name}' cez webové rozhranie.")
                    return jsonify({'success': False, 'message': f"Nepodarilo sa zakázať plugin '{plugin_name}'."}), 400
            except Exception as e:
                logger.error(f"Chyba pri zakázaní pluginu '{plugin_name}' cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/commands', methods=['GET'])
        def get_custom_commands():
            """Vráti vlastné príkazy."""
            if not self.custom_commands_manager:
                return jsonify({'error': 'Custom commands manager nie je dostupný'}), 500

            try:
                commands = []
                for cmd in self.custom_commands_manager.list_commands():
                    commands.append({
                        'name': cmd.name,
                        'triggers': cmd.triggers,
                        'action_type': cmd.action_type,
                        'description': cmd.description,
                        'usage_count': cmd.usage_count,
                        'action_data': cmd.action_data # Pridané action_data pre detail
                    })

                return jsonify(commands)
            except Exception as e:
                logger.error(f"Chyba pri načítaní vlastných príkazov cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/commands', methods=['POST'])
        def add_custom_command():
            """Pridá nový vlastný príkaz."""
            if not self.custom_commands_manager:
                return jsonify({'error': 'Custom commands manager nie je dostupný'}), 500

            try:
                data = request.get_json()
                if not isinstance(data, dict):
                     return jsonify({'error': 'Neplatný formát dát'}), 400

                # Základná validácia vstupných dát
                required_keys = ['name', 'triggers', 'action_type', 'action_data']
                if not all(key in data for key in required_keys):
                     return jsonify({'error': f'Chýbajú povinné polia: {", ".join(required_keys)}'}), 400

                from custom_voice_commands import CustomCommand # Zabezpečiť import

                command = CustomCommand(
                    name=data['name'],
                    triggers=data['triggers'],
                    action_type=data['action_type'],
                    action_data=data['action_data'],
                    description=data.get('description', '')
                )

                success = self.custom_commands_manager.add_command(command)
                if success:
                    logger.info(f"Vlastný príkaz '{command.name}' pridaný cez webové rozhranie.")
                    return jsonify({'success': True, 'message': f"Príkaz '{command.name}' bol úspešne pridaný."})
                else:
                    logger.warning(f"Vlastný príkaz '{command.name}' sa nepodarilo pridať (možno už existuje).")
                    return jsonify({'success': False, 'message': f"Príkaz '{command.name}' sa nepodarilo pridať (možno už existuje)."}), 409 # Conflict

            except Exception as e:
                logger.error(f"Chyba pri pridávaní vlastného príkazu cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/commands/<command_name>', methods=['DELETE'])
        def delete_custom_command(command_name):
            """Odstráni vlastný príkaz."""
            if not self.custom_commands_manager:
                return jsonify({'error': 'Custom commands manager nie je dostupný'}), 500

            try:
                success = self.custom_commands_manager.remove_command(command_name)
                if success:
                    logger.info(f"Vlastný príkaz '{command_name}' odstránený cez webové rozhranie.")
                    return jsonify({'success': True, 'message': f"Príkaz '{command_name}' bol úspešne odstránený."})
                else:
                    logger.warning(f"Vlastný príkaz '{command_name}' sa nepodarilo odstrániť (možno neexistuje).")
                    return jsonify({'success': False, 'message': f"Príkaz '{command_name}' sa nepodarilo odstrániť (možno neexistuje)."}), 404 # Not Found
            except Exception as e:
                logger.error(f"Chyba pri odstraňovaní vlastného príkazu '{command_name}' cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/status', methods=['GET'])
        def get_status():
            """Vráti status aplikácie."""
            try:
                status = {
                    'web_interface': self.running, # Skutočný stav web rozhrania
                    'config_manager': self.config_manager is not None,
                    'plugin_manager': self.plugin_manager is not None,
                    'custom_commands_manager': self.custom_commands_manager is not None,
                    'loaded_plugins': len(self.plugin_manager.list_loaded_plugins()) if self.plugin_manager else 0,
                    'custom_commands': len(self.custom_commands_manager.list_commands()) if self.custom_commands_manager else 0,
                    'weather_api': self._check_weather_api(),
                    'voice_recognition': True,  # Predpokladáme, že je dostupné (zatiaľ bez dynamickej kontroly)
                    'camera': self._check_camera(),
                    'uptime': self._get_uptime()
                }

                return jsonify(status)
            except Exception as e:
                logger.error(f"Chyba pri získavaní statusu cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/weather', methods=['GET'])
        def get_weather():
            """Vráti aktuálne počasie."""
            try:
                # Import weather funkcionalite
                # Zabezpečiť, že main modul je dostupný v sys.path
                import sys
                if '.' not in sys.path:
                    sys.path.append('.')
                from main import handle_weather_command # Import z main

                city = request.args.get('city', 'Bratislava')
                command = f"aké je počasie v {city}" if city.lower() != 'bratislava' else "aké je počasie"

                handled, response = handle_weather_command(command)

                if handled:
                    return jsonify({
                        'success': True,
                        'message': response,
                        'city': city
                    })
                else:
                    # Ak handle_weather_command vráti False, znamená to, že príkaz nebol weather príkaz
                    # V tomto prípade by sme mali vrátiť demo dáta alebo chybu, ak API nie je dostupné
                    # handle_weather_command už vracia demo dáta, ak API zlyhá, takže by handled malo byť True
                    # Ak by sa sem dostalo s handled=False, znamená to, že príkaz nebol rozpoznaný ako weather
                     return jsonify({
                        'success': False,
                        'message': f'Nepodarilo sa získať počasie pre mesto {city}. Skúste iný názov alebo skontrolujte API kľúč.',
                        'city': city
                    }), 404 # Not Found alebo 400 Bad Request

            except ImportError:
                 logger.error("Modul 'main' alebo 'handle_weather_command' nie je dostupný pre weather API.")
                 return jsonify({'error': 'Interná chyba servera: Weather modul nie je dostupný.'}), 500
            except Exception as e:
                logger.error(f"Chyba pri získavaní počasia cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/voice/test', methods=['POST'])
        def test_voice():
            """Test hlasového príkazu."""
            try:
                data = request.get_json()
                command_text = data.get('command', '')

                if not command_text:
                    return jsonify({'error': 'Príkaz nie je zadaný'}), 400

                # Simulácia spracovania príkazu pomocou logiky z main
                # Zabezpečiť, že main modul je dostupný v sys.path
                import sys
                if '.' not in sys.path:
                    sys.path.append('.')
                from main import handle_weather_command # Import z main
                # Ak by ste chceli testovať aj Gemini, potrebovali by ste inštanciu Cursor/Gemini tu

                # Skús weather príkaz
                weather_handled, weather_response = handle_weather_command(command_text)
                if weather_handled:
                    return jsonify({
                        'success': True,
                        'response': weather_response,
                        'type': 'weather'
                    })

                # Ak nie je weather, skús vlastné príkazy (ak je manager dostupný)
                if self.custom_commands_manager:
                    # Toto je len simulácia, skutočné vykonanie príkazu by bolo komplexnejšie
                    # a vyžadovalo by prístup k Cursor inštancii a jej metódam (click, type, atď.)
                    # Pre účely testu len skontrolujeme, či príkaz existuje
                    matched_command = self.custom_commands_manager.find_command(command_text)
                    if matched_command:
                         # Simulácia vykonania a odpovede
                         simulated_response = f"Simulované vykonanie príkazu '{matched_command.name}' (Typ: {matched_command.action_type})"
                         return jsonify({
                            'success': True,
                            'response': simulated_response,
                            'type': f'custom_{matched_command.action_type}'
                        })


                # Inak vráť základnú odpoveď (simulácia nerozpoznaného príkazu)
                return jsonify({
                    'success': False,
                    'error': f"Príkaz '{command_text}' nebol rozpoznaný ako weather alebo vlastný príkaz.",
                    'type': 'unrecognized'
                })

            except ImportError:
                 logger.error("Modul 'main' alebo 'handle_weather_command' nie je dostupný pre testovanie hlasu.")
                 return jsonify({'error': 'Interná chyba servera: Testovací modul nie je dostupný.'}), 500
            except Exception as e:
                logger.error(f"Chyba pri testovaní hlasového príkazu cez API: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/logs', methods=['GET'])
        def get_logs():
            """Vráti posledné logy."""
            try:
                # Čítanie logov z log súboru ak existuje
                # Predpokladáme, že logovanie je nastavené do súboru 'aircursor.log'
                log_file = 'aircursor.log'
                log_path = Path(log_file)

                if log_path.exists():
                    with log_path.open('r', encoding='utf-8', errors='ignore') as f: # Pridané errors='ignore' pre robustnosť
                        lines = f.readlines()
                        # Posledných 100 riadkov (zvýšený limit pre lepšiu diagnostiku)
                        recent_logs = lines[-100:] if len(lines) > 100 else lines
                        return jsonify({
                            'logs': [line.strip() for line in recent_logs],
                            'total_lines': len(lines)
                        })
                else:
                    return jsonify({
                        'logs': ['Log súbor neexistuje (aircursor.log)'],
                        'total_lines': 0
                    })

            except Exception as e:
                logger.error(f"Chyba pri načítaní logov cez API: {e}")
                return jsonify({'error': str(e)}), 500

        # --- Nové routes pre správu servera ---
        @self.app.route('/shutdown', methods=['POST'])
        def shutdown():
            """Vypne Flask server."""
            logger.info("Prijatá požiadavka na vypnutie webového rozhrania.")
            shutdown_func = request.environ.get('werkzeug.server.shutdown')
            if shutdown_func is None:
                raise RuntimeError('Not running with the Werkzeug Server')
            shutdown_func()
            return 'Server shutting down...'
        # --- Koniec nových routes ---

        @self.app.route('/api/info', methods=['GET'])
        def get_app_info():
            """Vrací základní informace o aplikaci a aktuální stav systému."""
            try:
                info = {
                    'name': 'AirCursor Assistant',
                    'version': '1.0.0',
                    'description': 'Webové rozhraní pro konfiguraci a správu AirCursor Assistant.',
                    'features': [
                        'Konfigurace systému',
                        'Správa pluginů',
                        'Hlasové příkazy',
                        'Monitoring systému',
                        'Počasí',
                        'Logy systému'
                    ],
                    'system_status': {
                        'uptime': self._get_uptime(),
                        'camera_available': self._check_camera(),
                        'weather_api_configured': self._check_weather_api()
                    }
                }
                return jsonify(info)
            except Exception as e:
                return jsonify({'error': str(e)}), 500


    def _check_weather_api(self) -> bool:
        """Skontroluje dostupnosť weather API (na základe API kľúča)."""
        try:
            import os
            # Používame OPENWEATHER_API_KEY ako definované v main.py handle_weather_command
            api_key = os.getenv('OPENWEATHER_API_KEY')
            # Základná kontrola, či kľúč existuje a nie je prázdny
            return api_key is not None and len(api_key) > 0
        except Exception as e:
            logger.warning(f"Chyba pri kontrole weather API kľúča: {e}")
            return False # Ak nastane chyba (napr. os modul), predpokladáme nedostupnosť

    def _check_camera(self) -> bool:
        """Skontroluje dostupnosť kamery."""
        try:
            import cv2
            # Skúsi otvoriť defaultnú kameru (index 0)
            cap = cv2.VideoCapture(0)
            is_open = cap.isOpened()
            cap.release() # Uvoľniť kameru ihneď
            return is_open
        except Exception as e:
            logger.warning(f"Chyba pri kontrole kamery: {e}")
            return False # Ak nastane chyba, predpokladáme nedostupnosť

    def _get_uptime(self) -> str:
        """Vráti uptime aplikácie."""
        try:
            if not hasattr(self, 'start_time'):
                 return "Neznámy (čas spustenia nebol zaznamenaný)"

            uptime_seconds = time.time() - self.start_time
            # Formátovanie uptime do čitateľného formátu (dni, hodiny, minúty, sekundy)
            days, remainder = divmod(uptime_seconds, 86400)
            hours, remainder = divmod(remainder, 3600)
            minutes, seconds = divmod(remainder, 60)

            parts = []
            if days > 0:
                parts.append(f"{int(days)}d")
            if hours > 0:
                parts.append(f"{int(hours)}h")
            if minutes > 0:
                parts.append(f"{int(minutes)}m")
            # Zobraziť sekundy len ak je uptime menej ako minúta
            if seconds > 0 and not parts:
                 parts.append(f"{int(seconds)}s")
            elif seconds > 0 and minutes > 0: # Zobraziť sekundy ak sú aj minúty
                 parts.append(f"{int(seconds)}s")


            return " ".join(parts) if parts else "menej ako 1s"

        except Exception as e:
            logger.error(f"Chyba pri výpočte uptime: {e}")
            return "Neznámy"


    # --- Nové metódy pre spustenie a zastavenie servera ---
    def start(self, open_browser: bool = False):
        """Spustí webový server v samostatnom vlákne."""
        if self.running:
            logger.info("Webové rozhranie už beží.")
            if open_browser:
                webbrowser.open(f"http://127.0.0.1:{self.port}")
            return

        logger.info(f"Spúšťam webové rozhranie na porte {self.port}...")
        self.running = True
        self.start_time = time.time() # Zaznamenať čas spustenia

        def run_server():
            try:
                # use_reloader=False je dôležité, aby sa Flask nespustil dvakrát vo vlákne
                # debug=False v produkcii
                self.app.run(port=self.port, debug=False, use_reloader=False)
            except Exception as e:
                logger.error(f"Chyba pri spúšťaní Flask servera: {e}")
                self.running = False # Zabezpečiť reset stavu pri chybe

        # Daemon vlákno sa automaticky ukončí, keď sa hlavný program ukončí
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()

        if open_browser:
            # Počkať krátko, aby sa server stihol spustiť, kým otvoríme prehliadač
            threading.Timer(1, lambda: webbrowser.open(f"http://127.0.0.1:{self.port}")).start()

    def stop(self):
        """Zastaví webový server."""
        if not self.running:
            logger.info("Webové rozhranie už nebeží.")
            return

        logger.info("Zastavujem webové rozhranie...")
        self.running = False # Nastaviť stav na zastavený

        # Pošle POST požiadavku na shutdown endpoint
        try:
            import requests
            # Používame 127.0.0.1 namiesto localhost pre konzistentnosť
            requests.post(f"http://127.0.0.1:{self.port}/shutdown")
        except Exception as e:
            logger.warning(f"Nepodarilo sa poslať shutdown požiadavku na web server: {e}")

        # Počkať na ukončenie vlákna servera
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=5) # Počkať maximálne 5 sekúnd
            if self.server_thread.is_alive():
                 logger.warning("Vlákno web servera sa neukončilo čisto.")
            else:
                 logger.info("Vlákno web servera úspešne ukončené.")

    # --- Koniec nových metód ---


    def _get_index_template(self) -> str:
        """Vráti HTML template pre hlavnú stránku."""
        return '''
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirCursor Assistant - Pokročilé webové rozhranie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
        }
        .header h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .header p {
            color: #718096;
            font-size: 1.2em;
        }
        .header .version {
            position: absolute;
            top: 15px;
            right: 20px;
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            display: flex; /* Použijeme flexbox pre lepšie rozloženie obsahu */
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h2 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.5em;
            border-bottom: 1px solid #eee; /* Oddelovač nadpisu */
            padding-bottom: 10px;
        }
        .card-content {
            flex-grow: 1; /* Obsah karty zaberie dostupné miesto */
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px 0; /* Upravené okraje */
            display: inline-block; /* Aby sa tlačidlá zobrazovali vedľa seba ak je miesto */
            text-decoration: none; /* Pre prípad, že by to bol link */
        }
         .btn + .btn {
             margin-left: 10px; /* Medzera medzi tlačidlami */
         }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            margin-left: 10px; /* Medzera od textu */
        }
        .status.online { background: #48bb78; color: white; }
        .status.offline { background: #f56565; color: white; }
        .loading {
            text-align: center;
            padding: 20px;
            color: #718096;
        }
        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            word-break: break-word; /* Zalamovanie dlhých chybových správ */
        }
        .success {
            background: #c6f6d5;
            color: #2f855a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            word-break: break-word;
        }
        .info-item {
            margin-bottom: 8px; /* Medzera medzi status položkami */
        }
        .log-container {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap; /* Zachováva zalomenie riadkov v logoch */
            word-break: break-all; /* Zalamuje dlhé slová */
        }
        input[type="text"] {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 1em;
            flex-grow: 1; /* Input zaberie dostupné miesto */
        }
        .input-group {
            display: flex; /* Flexbox pre input a tlačidlo */
            align-items: center;
            margin-top: 15px;
        }
         .input-group .btn {
             margin-top: 0; /* Upraviť okraj tlačidla vo flexboxe */
             margin-bottom: 0;
         }
         .card-actions {
             margin-top: 15px;
             padding-top: 15px;
             border-top: 1px solid #eee;
         }

    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AirCursor Assistant</h1>
            <p>Webové rozhranie pre konfiguráciu a správu</p>
        </div>

        <div class="grid">
            <div class="card">
                <h2>📊 Status systému</h2>
                <div id="status-content" class="loading card-content">Načítavam...</div>
            </div>

            <div class="card">
                <h2>🌤️ Počasie</h2>
                <div id="weather-content" class="loading card-content">Načítavam...</div>
                <div class="card-actions">
                    <div class="input-group">
                        <input type="text" id="city-input" placeholder="Zadajte mesto...">
                        <button class="btn" onclick="getWeatherForCity()">Získať počasie</button>
                    </div>
                </div>
            </div>

            <div class="card">
                <h2>🎤 Test hlasových príkazov</h2>
                <div id="voice-test-content" class="card-content">
                    <input type="text" id="command-input" placeholder="Zadajte hlasový príkaz..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;">
                    <button class="btn" onclick="testVoiceCommand()">Testovať príkaz</button>
                    <div id="voice-response" style="margin-top: 15px;"></div>
                </div>
            </div>

            <div class="card">
                <h2>⚙️ Konfigurácia</h2>
                <div id="config-content" class="loading card-content">Načítavam...</div>
                 <div class="card-actions">
                     <button class="btn" onclick="alert('Úprava konfigurácie bude dostupná v budúcej verzii')">Upraviť konfiguráciu</button>
                 </div>
            </div>

            <div class="card">
                <h2>🔌 Pluginy</h2>
                <div id="plugins-content" class="loading card-content">Načítavam...</div>
                 <div class="card-actions">
                     <button class="btn" onclick="alert('Správa pluginov (povoliť/zakázať) bude dostupná v budúcej verzii')">Spravovať pluginy</button>
                 </div>
            </div>

            <div class="card">
                <h2>🎤 Vlastné príkazy</h2>
                <div id="commands-content" class="loading card-content">Načítavam...</div>
                 <div class="card-actions">
                     <button class="btn" onclick="alert('Pridávanie a mazanie príkazov bude dostupné v budúcej verzii')">Spravovať príkazy</button>
                 </div>
            </div>

            <div class="card">
                <h2>📋 Logy systému</h2>
                <div id="logs-content" class="loading card-content">Načítavam...</div>
                <div class="card-actions">
                    <button class="btn" onclick="refreshLogs()">Obnoviť logy</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Helper funkcia pre fetch s error handlingom
        async function fetchData(url, options = {}) {
            try {
                const response = await fetch(url, options);
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP error! status: ${response.status}, detail: ${errorText}`);
                }
                return await response.json();
            } catch (error) {
                console.error("Fetch error:", error);
                throw error; // Preposlať chybu ďalej
            }
        }


        // Načítanie status
        async function loadStatus() {
            const statusContentDiv = document.getElementById('status-content');
            statusContentDiv.innerHTML = '<div class="loading">Načítavam...</div>';
            try {
                const data = await fetchData('/api/status');
                const statusHtml = `
                    <div class="info-item"><strong>Webové rozhranie:</strong> <span class="status ${data.web_interface ? 'online' : 'offline'}">${data.web_interface ? 'Beží' : 'Zastavené'}</span></div>
                    <div class="info-item"><strong>Config Manager:</strong> <span class="status ${data.config_manager ? 'online' : 'offline'}">${data.config_manager ? 'Aktívny' : 'Neaktívny'}</span></div>
                    <div class="info-item"><strong>Plugin Manager:</strong> <span class="status ${data.plugin_manager ? 'online' : 'offline'}">${data.plugin_manager ? 'Aktívny' : 'Neaktívny'}</span></div>
                    <div class="info-item"><strong>Custom Commands:</strong> <span class="status ${data.custom_commands_manager ? 'online' : 'offline'}">${data.custom_commands_manager ? 'Aktívny' : 'Neaktívny'}</span></div>
                    <div class="info-item"><strong>Weather API Kľúč:</strong> <span class="status ${data.weather_api ? 'online' : 'offline'}">${data.weather_api ? 'Konfigurovaný' : 'Chýba'}</span></div>
                    <div class="info-item"><strong>Kamera:</strong> <span class="status ${data.camera ? 'online' : 'offline'}">${data.camera ? 'Dostupná' : 'Nedostupná'}</span></div>
                    <div class="info-item"><strong>Načítané pluginy:</strong> ${data.loaded_plugins}</div>
                    <div class="info-item"><strong>Vlastné príkazy:</strong> ${data.custom_commands}</div>
                    <div class="info-item"><strong>Uptime:</strong> ${data.uptime}</div>
                `;
                statusContentDiv.innerHTML = statusHtml;
            } catch (error) {
                statusContentDiv.innerHTML = `<div class="error">Chyba pri načítaní status: ${error.message}</div>`;
            }
        }

        // Načítanie počasia
        async function loadWeather() {
            const weatherContentDiv = document.getElementById('weather-content');
            weatherContentDiv.innerHTML = '<div class="loading">Načítavam...</div>';
            try {
                const data = await fetchData('/api/weather');
                if (data.success) {
                    weatherContentDiv.innerHTML = `
                        <div class="success">
                            <strong>📍 ${data.city}:</strong><br>
                            ${data.message}
                        </div>
                    `;
                } else {
                    weatherContentDiv.innerHTML = `
                        <div class="error">${data.message}</div>
                    `;
                }
            } catch (error) {
                weatherContentDiv.innerHTML = `<div class="error">Chyba pri načítaní počasia: ${error.message}</div>`;
            }
        }

        // Načítanie konfigurácie
        async function loadConfig() {
             const configContentDiv = document.getElementById('config-content');
             configContentDiv.innerHTML = '<div class="loading">Načítavam...</div>';
             try {
                 const data = await fetchData('/api/config');
                 let configHtml = '<p>Aktuálna konfigurácia:</p>';
                 // Zobrazíme len zjednodušený prehľad
                 for (const section in data) {
                     configHtml += `<strong>${section.charAt(0).toUpperCase() + section.slice(1)}:</strong> `;
                     const keys = Object.keys(data[section]);
                     if (keys.length > 0) {
                         configHtml += keys.join(', ');
                     } else {
                         configHtml += 'žiadne nastavenia';
                     }
                     configHtml += '<br>';
                 }

                 configContentDiv.innerHTML = configHtml;
             } catch (error) {
                 configContentDiv.innerHTML = `<div class="error">Chyba pri načítaní konfigurácie: ${error.message}</div>`;
             }
         }


        // Načítanie pluginov
        async function loadPlugins() {
            const pluginsContentDiv = document.getElementById('plugins-content');
            pluginsContentDiv.innerHTML = '<div class="loading">Načítavam...</div>';
            try {
                const data = await fetchData('/api/plugins');
                let pluginsHtml = '';
                if (data.length === 0) {
                    pluginsHtml = '<p>Žiadne pluginy nie sú nainštalované.</p>';
                } else {
                    data.forEach(plugin => {
                        pluginsHtml += `
                            <div style="border: 1px solid #e2e8f0; padding: 15px; margin: 10px 0; border-radius: 8px;">
                                <h3>${plugin.name} v${plugin.version}</h3>
                                <p>${plugin.description}</p>
                                <p><strong>Autor:</strong> ${plugin.author}</p>
                                <p><strong>Status:</strong> <span class="status ${plugin.enabled ? 'online' : 'offline'}">${plugin.enabled ? 'Povolený' : 'Zakázaný'}</span></p>
                                <p><strong>Načítaný:</strong> <span class="status ${plugin.loaded ? 'online' : 'offline'}">${plugin.loaded ? 'Áno' : 'Nie'}</span></p>
                            </div>
                        `;
                    });
                }
                pluginsContentDiv.innerHTML = pluginsHtml;
            } catch (error) {
                pluginsContentDiv.innerHTML = `<div class="error">Chyba pri načítaní pluginov: ${error.message}</div>`;
            }
        }

        // Načítanie vlastných príkazov
        async function loadCommands() {
            const commandsContentDiv = document.getElementById('commands-content');
            commandsContentDiv.innerHTML = '<div class="loading">Načítavam...</div>';
            try {
                const data = await fetchData('/api/commands');
                let commandsHtml = '';
                if (data.length === 0) {
                    commandsHtml = '<p>Žiadne vlastné príkazy nie sú definované.</p>';
                } else {
                    data.forEach(cmd => {
                        commandsHtml += `
                            <div style="border: 1px solid #e2e8f0; padding: 15px; margin: 10px 0; border-radius: 8px;">
                                <h3>${cmd.name}</h3>
                                <p>${cmd.description || 'Bez popisu'}</p>
                                <p><strong>Triggery:</strong> ${cmd.triggers.join(', ')}</p>
                                <p><strong>Typ:</strong> ${cmd.action_type}</p>
                                <p><strong>Použité:</strong> ${cmd.usage_count}x</p>
                                <!-- Zobrazenie action_data môže byť detailnejšie -->
                                <details><summary>Detail akcie</summary><pre>${JSON.stringify(cmd.action_data, null, 2)}</pre></details>
                            </div>
                        `;
                    });
                }
                commandsContentDiv.innerHTML = commandsHtml;
            } catch (error) {
                commandsContentDiv.innerHTML = `<div class="error">Chyba pri načítaní príkazov: ${error.message}</div>`;
            }
        }

        // Načítanie logov
        async function loadLogs() {
            const logsContentDiv = document.getElementById('logs-content');
            logsContentDiv.innerHTML = '<div class="loading">Načítavam...</div>';
            try {
                const data = await fetchData('/api/logs');
                let logsHtml = `<p><strong>Celkom riadkov:</strong> ${data.total_lines}</p>`;
                logsHtml += '<div class="log-container">';

                if (data.logs.length === 0) {
                    logsHtml += '<p>Žiadne logy nie sú dostupné.</p>';
                } else {
                    data.logs.forEach(log => {
                        // Základné sfarbenie logov podľa úrovne
                        let logColor = '#333'; // Default
                        if (log.includes('ERROR')) logColor = '#c53030';
                        else if (log.includes('WARNING')) logColor = '#dd6b20';
                        else if (log.includes('INFO')) logColor = '#2f855a';
                        else if (log.includes('DEBUG')) logColor = '#718096';

                        logsHtml += `<div style="margin-bottom: 2px; color: ${logColor};">${log}</div>`;
                    });
                }

                logsHtml += '</div>';
                logsContentDiv.innerHTML = logsHtml;
            } catch (error) {
                logsContentDiv.innerHTML = `<div class="error">Chyba pri načítaní logov: ${error.message}</div>`;
            }
        }

        // Funkcie pre interakciu
        async function getWeatherForCity() {
            const cityInput = document.getElementById('city-input');
            const weatherContentDiv = document.getElementById('weather-content');
            const city = cityInput.value.trim();

            if (!city) {
                weatherContentDiv.innerHTML = '<div class="error">Zadajte názov mesta.</div>';
                return;
            }

            weatherContentDiv.innerHTML = '<div class="loading">Načítavam počasie pre ' + city + '...</div>';

            try {
                const data = await fetchData('/api/weather?city=' + encodeURIComponent(city));
                if (data.success) {
                    weatherContentDiv.innerHTML = `
                        <div class="success">
                            <strong>📍 ${data.city}:</strong><br>
                            ${data.message}
                        </div>
                    `;
                } else {
                    weatherContentDiv.innerHTML = `
                        <div class="error">${data.message}</div>
                    `;
                }
            } catch (error) {
                weatherContentDiv.innerHTML = `<div class="error">Chyba pri načítaní počasia pre ${city}: ${error.message}</div>`;
            }
        }

        async function testVoiceCommand() {
            const commandInput = document.getElementById('command-input');
            const voiceResponseDiv = document.getElementById('voice-response');
            const command = commandInput.value.trim();

            if (!command) {
                voiceResponseDiv.innerHTML = '<div class="error">Zadajte príkaz na testovanie.</div>';
                return;
            }

            voiceResponseDiv.innerHTML = '<div class="loading">Testujem príkaz...</div>';

            try {
                const data = await fetchData('/api/voice/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ command: command }),
                });

                if (data.success) {
                     voiceResponseDiv.innerHTML = `
                        <div class="success">
                            <strong>Odpoveď:</strong> ${data.response}
                            <br>
                            <strong>Typ:</strong> ${data.type}
                        </div>
                    `;
                } else {
                    voiceResponseDiv.innerHTML = `
                        <div class="error">${data.error || 'Neznáma chyba pri testovaní príkazu'}</div>
                    `;
                }
            } catch (error) {
                voiceResponseDiv.innerHTML = `<div class="error">Chyba pri testovaní príkazu: ${error.message}</div>`;
            }
        }

        function refreshLogs() {
            loadLogs(); // Stačí zavolať funkciu na načítanie logov
        }

        // Načítať dáta pri načítaní stránky
        document.addEventListener('DOMContentLoaded', () => {
            loadStatus();
            loadWeather();
            loadConfig();
            loadPlugins();
            loadCommands();
            loadLogs();

            // Voliteľné: Automatické obnovovanie statusu a logov
            // setInterval(loadStatus, 10000); // Obnoviť status každých 10 sekúnd
            // setInterval(loadLogs, 5000);   // Obnoviť logy každých 5 sekúnd
        });

    </script>
</body>
</html>
'''

# Príklad použitia (ak by sa spúšťalo samostatne)
if __name__ == '__main__':
    # Toto je len pre testovanie WebInterface samostatne
    # V reálnej aplikácii sa spúšťa z main.py
    print("Spúšťam len webové rozhranie pre testovacie účely.")
    print("Pre plnú funkčnosť spustite main.py")

    # Simulácia dummy manažérov pre test
    class DummyManager:
        def list_plugins(self): return [{'name': 'DummyPlugin', 'version': '1.0', 'description': 'Testovací plugin', 'author': 'Test', 'enabled': True, 'loaded': True}]
        def list_loaded_plugins(self): return self.list_plugins()
        def enable_plugin(self, name): return True
        def disable_plugin(self, name): return True
        def list_commands(self): return [{'name': 'TestCommand', 'triggers': ['test'], 'action_type': 'script', 'description': 'Testovací príkaz', 'usage_count': 0, 'action_data': {'script': 'print("Hello")'}}]
        def add_command(self, cmd): return True
        def remove_command(self, name): return True
        def get_all_sections(self): return ['general', 'test']
        def get(self, section, key=None, default=None):
            if section == 'general': return {'setting1': 'value1'}
            if section == 'test': return {'test_key': 123}
            return default
        def set(self, section, key, value): pass
        def save(self): pass
        def find_command(self, text):
             if 'test' in text.lower():
                 return type('obj', (object,), {'name': 'TestCommand', 'action_type': 'script'})() # Dummy object
             return None


    dummy_config = DummyManager()
    dummy_plugins = DummyManager()
    dummy_commands = DummyManager()

    # Nastavenie dummy API kľúča pre test weather API kontroly
    os.environ['OPENWEATHER_API_KEY'] = 'dummy_key_for_test'

    web_interface = WebInterface(
        config_manager=dummy_config,
        plugin_manager=dummy_plugins,
        custom_commands_manager=dummy_commands,
        port=8080
    )

    # Spustenie servera
    web_interface.start(open_browser=True)

    # Aby hlavné vlákno neskončilo hneď (pre testovanie daemon=True)
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Ukončujem test webového rozhrania.")
        web_interface.stop() # Pokus o čisté ukončenie

