@echo off
echo.
echo ========================================
echo  AirCursor Assistant - Web Interface
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python nie je nainstalovany alebo nie je v PATH
    echo Nainštalujte Python z https://python.org
    pause
    exit /b 1
)

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo Aktivujem virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo WARNING: Virtual environment nebol najdeny
    echo Pokracujem s globalnym Python...
)

REM Start web interface
echo Spustam webove rozhranie...
echo.
python web_launcher.py

pause
