#!/usr/bin/env python3
"""
Uk<PERSON><PERSON><PERSON><PERSON> Weather Plugin pre AirCursor Assistant

Poskytuje informácie o počasí cez hlasové príkazy.
"""

import requests
import json
from typing import Dict, Any, Callable, Optional
import logging

# Import plugin interface
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from plugin_system import PluginInterface

logger = logging.getLogger(__name__)

class WeatherPlugin(PluginInterface):
    """Plugin pre informácie o počasí."""
    
    @property
    def name(self) -> str:
        return "weather_plugin"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "Poskytuje informácie o počasí cez hlasové príkazy"
    
    @property
    def author(self) -> str:
        return "AirCursor Team"
    
    @property
    def dependencies(self) -> list:
        return ["requests"]
    
    @property
    def min_aircursor_version(self) -> str:
        return "1.3.0"
    
    def __init__(self):
        self.api_key = None
        self.default_city = "Bratislava"
        self.base_url = "http://api.openweathermap.org/data/2.5/weather"
        self.initialized = False
    
    def initialize(self, context: Dict[str, Any]) -> bool:
        """Inicializuje plugin."""
        try:
            # Načítanie konfigurácie
            config = context.get('config', {})
            self.api_key = config.get('weather_api_key')
            self.default_city = config.get('default_city', 'Bratislava')
            
            if not self.api_key:
                logger.warning("Weather plugin: API kľúč nie je nastavený")
                # Pre demo účely použijeme mock dáta
                self.api_key = "demo"
            
            self.initialized = True
            logger.info("Weather plugin úspešne inicializovaný")
            return True
            
        except Exception as e:
            logger.error(f"Chyba pri inicializácii weather plugin: {e}")
            return False
    
    def cleanup(self) -> bool:
        """Vyčistí zdroje pluginu."""
        self.initialized = False
        logger.info("Weather plugin vyčistený")
        return True
    
    def get_voice_commands(self) -> Dict[str, Callable]:
        """Vráti hlasové príkazy pluginu."""
        return {
            "aké je počasie": self.get_current_weather,
            "počasie dnes": self.get_current_weather,
            "teplota vonku": self.get_temperature,
            "počasie v": self.get_weather_for_city,
            "predpoveď počasia": self.get_weather_forecast
        }
    
    def on_voice_command(self, command: str) -> Optional[Any]:
        """Spracuje hlasový príkaz."""
        command_lower = command.lower()
        
        if "počasie" in command_lower:
            if "v " in command_lower:
                # Extrakcia názvu mesta
                city = self._extract_city_from_command(command)
                return self.get_weather_for_city(city)
            else:
                return self.get_current_weather()
        
        elif "teplota" in command_lower:
            return self.get_temperature()
        
        elif "predpoveď" in command_lower:
            return self.get_weather_forecast()
        
        return None
    
    def get_current_weather(self) -> Dict[str, Any]:
        """Vráti aktuálne počasie."""
        try:
            if self.api_key == "demo":
                return self._get_demo_weather()
            
            url = f"{self.base_url}?q={self.default_city}&appid={self.api_key}&units=metric&lang=sk"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return self._format_weather_data(data)
            else:
                return {"error": "Nepodarilo sa získať údaje o počasí"}
                
        except Exception as e:
            logger.error(f"Chyba pri získavaní počasia: {e}")
            return {"error": str(e)}
    
    def get_temperature(self) -> Dict[str, Any]:
        """Vráti aktuálnu teplotu."""
        weather = self.get_current_weather()
        if "error" in weather:
            return weather
        
        return {
            "temperature": weather.get("temperature"),
            "feels_like": weather.get("feels_like"),
            "message": f"Aktuálna teplota je {weather.get('temperature')}°C, pocitovo {weather.get('feels_like')}°C"
        }
    
    def get_weather_for_city(self, city: str = None) -> Dict[str, Any]:
        """Vráti počasie pre konkrétne mesto."""
        if not city:
            city = self.default_city
        
        try:
            if self.api_key == "demo":
                return self._get_demo_weather(city)
            
            url = f"{self.base_url}?q={city}&appid={self.api_key}&units=metric&lang=sk"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return self._format_weather_data(data, city)
            else:
                return {"error": f"Nepodarilo sa získať údaje o počasí pre {city}"}
                
        except Exception as e:
            logger.error(f"Chyba pri získavaní počasia pre {city}: {e}")
            return {"error": str(e)}
    
    def get_weather_forecast(self) -> Dict[str, Any]:
        """Vráti predpoveď počasia."""
        # Pre demo účely vrátime jednoduchú predpoveď
        return {
            "forecast": "Predpoveď na najbližšie dni: Dnes slnečno 22°C, zajtra oblačno 18°C, pozajtra dážď 15°C",
            "message": "Predpoveď počasia na najbližšie dni je dostupná"
        }
    
    def _format_weather_data(self, data: Dict[str, Any], city: str = None) -> Dict[str, Any]:
        """Formátuje údaje o počasí."""
        try:
            city_name = city or data.get("name", self.default_city)
            temperature = round(data["main"]["temp"])
            feels_like = round(data["main"]["feels_like"])
            humidity = data["main"]["humidity"]
            description = data["weather"][0]["description"]
            
            message = f"V meste {city_name} je {temperature}°C, pocitovo {feels_like}°C. {description.capitalize()}. Vlhkosť {humidity}%."
            
            return {
                "city": city_name,
                "temperature": temperature,
                "feels_like": feels_like,
                "humidity": humidity,
                "description": description,
                "message": message
            }
            
        except KeyError as e:
            logger.error(f"Chyba pri formátovaní údajov o počasí: {e}")
            return {"error": "Neplatné údaje o počasí"}
    
    def _get_demo_weather(self, city: str = None) -> Dict[str, Any]:
        """Vráti demo údaje o počasí."""
        city_name = city or self.default_city
        
        # Simulované údaje
        demo_data = {
            "city": city_name,
            "temperature": 20,
            "feels_like": 22,
            "humidity": 65,
            "description": "jasno",
            "message": f"V meste {city_name} je 20°C, pocitovo 22°C. Jasno. Vlhkosť 65%. (Demo údaje)"
        }
        
        return demo_data
    
    def _extract_city_from_command(self, command: str) -> str:
        """Extrahuje názov mesta z hlasového príkazu."""
        command_lower = command.lower()
        
        # Hľadáme vzor "počasie v [mesto]"
        if "v " in command_lower:
            parts = command_lower.split("v ")
            if len(parts) > 1:
                city = parts[1].strip()
                # Odstránenie možných koncových slov
                city = city.replace("?", "").replace(".", "").strip()
                return city.title()
        
        return self.default_city
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Vráti schému konfigurácie pluginu."""
        return {
            "weather_api_key": {
                "type": "string",
                "description": "API kľúč pre OpenWeatherMap",
                "required": False,
                "default": ""
            },
            "default_city": {
                "type": "string",
                "description": "Predvolené mesto pre počasie",
                "required": False,
                "default": "Bratislava"
            }
        }

if __name__ == "__main__":
    # Test pluginu
    plugin = WeatherPlugin()
    
    # Test inicializácie
    context = {
        "config": {
            "weather_api_key": "demo",
            "default_city": "Bratislava"
        }
    }
    
    if plugin.initialize(context):
        print("✅ Plugin úspešne inicializovaný")
        
        # Test hlasových príkazov
        commands = plugin.get_voice_commands()
        print(f"📢 Dostupné hlasové príkazy: {list(commands.keys())}")
        
        # Test získania počasia
        weather = plugin.get_current_weather()
        print(f"🌤️  Počasie: {weather.get('message', weather)}")
        
        # Test teploty
        temp = plugin.get_temperature()
        print(f"🌡️  Teplota: {temp.get('message', temp)}")
        
        # Test hlasového príkazu
        result = plugin.on_voice_command("aké je počasie")
        print(f"🎤 Hlasový príkaz: {result.get('message', result) if result else 'Žiadna odpoveď'}")
        
        # Cleanup
        plugin.cleanup()
        print("✅ Plugin vyčistený")
    else:
        print("❌ Chyba pri inicializácii pluginu")
