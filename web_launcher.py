#!/usr/bin/env python3
"""
Web-only launcher pre AirCursor Assistant

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> iba webové rozhranie bez GUI.
"""

import sys
import time
import logging
from pathlib import Path

# Pridanie project root do Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from web_interface import WebInterface
    from custom_voice_commands import CustomVoiceCommandManager
    from plugin_system import PluginManager
    WEB_INTERFACE_AVAILABLE = True
except ImportError as e:
    print(f"Chyba pri importovaní modulov: {e}")
    WEB_INTERFACE_AVAILABLE = False

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def main():
    """Hlavná funkcia web-only launchera."""
    # Nastavenie kódovania pre Windows konzolu
    import sys
    if sys.platform == "win32":
        import os
        os.system("chcp 65001 >nul 2>&1")  # UTF-8 kódovanie

    print("AirCursor Assistant - Web-only rezim")
    print("=" * 50)

    if not WEB_INTERFACE_AVAILABLE:
        print("ERROR: Webove rozhranie nie je dostupne!")
        print("Skontrolujte, ci su nainstalovane vsetky zavislosti:")
        print("pip install flask")
        return 1

    try:
        # Inicializácia manažérov
        print("Inicializujem manazerov...")
        custom_commands_manager = CustomVoiceCommandManager()
        plugin_manager = PluginManager()

        # Objavenie a načítanie pluginov
        plugin_manager.discover_plugins()

        # Inicializácia webového rozhrania
        print("Inicializujem webove rozhranie...")
        web_interface = WebInterface(
            config_manager=None,
            plugin_manager=plugin_manager,
            custom_commands_manager=custom_commands_manager,
            port=8080
        )

        # Spustenie webového servera
        print("Spustam webovy server na porte 8080...")
        web_interface.start(open_browser=True)

        print("SUCCESS: Webove rozhranie je spustene!")
        print("Otvorte prehliadac na: http://localhost:8080")
        print("Pre ukoncenie stlacte Ctrl+C")
        print("=" * 50)
        
        # Udržanie servera v chode
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Ukončujem webový server...")
            web_interface.stop()
            print("✅ Server úspešne ukončený.")
            
    except Exception as e:
        logger.error(f"Chyba pri spúšťaní web-only režimu: {e}")
        print(f"❌ Chyba: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
