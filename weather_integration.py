#!/usr/bin/env python3
"""
Jednoduchá integrácia weather pluginu pre voice commands

Tento modul poskytuje weather funkcionalitu bez komplexnej plugin architektúry.
"""

import logging

logger = logging.getLogger(__name__)

class SimpleWeatherIntegration:
    """Jednoduchá weather integrácia."""
    
    def __init__(self):
        self.weather_commands = {
            "aké je počasie": self.get_current_weather,
            "počasie dnes": self.get_current_weather,
            "teplota vonku": self.get_temperature,
            "aké bude počasie": self.get_forecast,
            "predpoveď počasia": self.get_forecast,
            "počasie v": self.get_weather_for_city
        }
    
    def process_weather_command(self, command: str) -> tuple[bool, str]:
        """Spracuje weather príkaz."""
        command_lower = command.lower().strip()
        
        # Presná zhoda
        if command_lower in self.weather_commands:
            try:
                result = self.weather_commands[command_lower]()
                return True, result
            except Exception as e:
                logger.error(f"Chyba pri spracovaní weather príkazu: {e}")
                return True, "Chyba pri získavaní informácií o počasí."
        
        # Čiastočná zhoda
        for trigger, func in self.weather_commands.items():
            if trigger in command_lower or any(word in command_lower for word in trigger.split()):
                try:
                    if "počasie v" in command_lower:
                        # Extrakcia mesta
                        city = self._extract_city(command_lower)
                        result = self.get_weather_for_city(city)
                    else:
                        result = func()
                    return True, result
                except Exception as e:
                    logger.error(f"Chyba pri spracovaní weather príkazu: {e}")
                    return True, "Chyba pri získavaní informácií o počasí."
        
        return False, ""
    
    def get_current_weather(self) -> str:
        """Vráti aktuálne počasie."""
        return "V Bratislave je momentálne 20°C, jasno. Vlhkosť 65%. (Demo údaje)"
    
    def get_temperature(self) -> str:
        """Vráti aktuálnu teplotu."""
        return "Aktuálna teplota je 20°C, pocitovo 22°C. (Demo údaje)"
    
    def get_forecast(self) -> str:
        """Vráti predpoveď počasia."""
        return "Predpoveď na najbližšie dni: Dnes slnečno 22°C, zajtra oblačno 18°C, pozajtra dážď 15°C. (Demo údaje)"
    
    def get_weather_for_city(self, city: str = "Bratislava") -> str:
        """Vráti počasie pre konkrétne mesto."""
        return f"V meste {city} je 20°C, jasno. Vlhkosť 65%. (Demo údaje)"
    
    def _extract_city(self, command: str) -> str:
        """Extrahuje názov mesta z príkazu."""
        if "v " in command:
            parts = command.split("v ")
            if len(parts) > 1:
                city = parts[1].strip()
                city = city.replace("?", "").replace(".", "").strip()
                return city.title()
        return "Bratislava"

# Globálna inštancia
_weather_integration = None

def get_weather_integration():
    """Vráti globálnu weather integráciu."""
    global _weather_integration
    if _weather_integration is None:
        _weather_integration = SimpleWeatherIntegration()
    return _weather_integration

def process_weather_command(command: str) -> tuple[bool, str]:
    """Spracuje weather príkaz."""
    integration = get_weather_integration()
    return integration.process_weather_command(command)

if __name__ == "__main__":
    # Test weather integrácie
    integration = SimpleWeatherIntegration()
    
    test_commands = [
        "aké je počasie",
        "počasie dnes",
        "teplota vonku", 
        "aké bude počasie",
        "predpoveď počasia",
        "počasie v Košiciach"
    ]
    
    print("🧪 Test weather integrácie")
    print("=" * 40)
    
    for command in test_commands:
        handled, response = integration.process_weather_command(command)
        status = "✅" if handled else "❌"
        print(f"{status} '{command}' -> {response}")
    
    print("\n🎉 Weather integrácia funguje!")
