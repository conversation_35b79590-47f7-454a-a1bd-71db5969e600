# 🎯 AirCursor Assistant v2.0

**Pokročilý webový asistent s AI integráciou a real-time monitoringom**

AirCursor Assistant je moderná webová aplik<PERSON>, k<PERSON><PERSON> kombinuje počíta<PERSON><PERSON> videnie, rozpoznávanie reči a umelú inteligenciu s pokročilým webovým rozhraním pre kompletnú správu systému.

## ✨ Hlavné funkcie

- 🌐 **Pokročilé webové rozhranie** - Moderný responsive design s real-time monitoringom
- 🖐️ **Ovládanie kurzora gestami ruky** - MediaPipe hand tracking
- 🎤 **Hlasové príkazy v slovenčine** - Google Speech Recognition
- 🤖 **AI asistent** - Integrácia s Gemini AI
- 📊 **Real-time monitoring** - Systémové štatistiky a grafy
- 📁 **Správa súborov** - Webový file manager
- 🔌 **Plugin systém** - Rozšíriteľnosť cez pluginy
- ⚙️ **Webová konfigurácia** - Kompletná správa nastavení
- 🔊 **Text-to-Speech** - Hlasová spätná väzba
- 📖 **OCR a webová interakcia** - Čítanie obrazovky a automatizácia

## 🚀 Rýchly štart

### Predpoklady
- Python 3.12+
- Webkamera
- Mikrofón a reproduktory/slúchadlá
- Gemini API kľúč

### Inštalácia

#### 🚀 Automatická inštalácia (odporúčané)

**Windows:**
```bash
install.bat
```

**Linux/macOS:**
```bash
./install.sh
```

**Univerzálne (Python):**
```bash
python install.py
```

#### 📋 Manuálna inštalácia

1. **Klonujte repository:**
```bash
git clone <repository-url>
cd aircursor-assistant
```

2. **Vytvorte virtual environment:**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

3. **Inštalujte závislosti:**
```bash
pip install -r requirements.txt
```

4. **Nastavte API kľúč:**
```bash
python setup_env.py
```

5. **Spustite aplikáciu:**
```bash
python main.py
```

#### 🛠️ Použitie Makefile (pre vývojárov)
```bash
make install    # Kompletná inštalácia
make setup      # Nastavenie API kľúča
make run        # Spustenie aplikácie
make help       # Zobrazí všetky dostupné príkazy
```

## 📋 Systémové požiadavky

### Povinné závislosti
- `opencv-python` - Spracovanie videa
- `mediapipe` - Hand tracking
- `pyautogui` - Ovládanie myši a klávesnice
- `speech-recognition` - Rozpoznávanie reči
- `gtts` - Text-to-speech
- `google-generativeai` - Gemini AI
- `tkinter` - GUI (zvyčajne súčasť Pythonu)

### Voliteľné závislosti
- `playsound3` - Prehrávanie audio súborov
- `pycaw` - Ovládanie hlasitosti Windows
- `screeninfo` - Informácie o obrazovke
- `selenium` - Webová automatizácia
- `pytesseract` - OCR

## 🎮 Používanie

### Kalibrácia
1. Spustite aplikáciu
2. Stlačte `A` pre automatickú kalibráciu
3. Ukážte rukou do 4 rohov obrazovky postupne
4. Kalibrácia sa automaticky uloží

### Hlasové príkazy

#### Základné ovládanie
- **"klikni"** - Ľavé kliknutie myši
- **"pravý klik"** - Pravé kliknutie myši
- **"dvojklik"** - Dvojité kliknutie
- **"roluj hore/dole"** - Posúvanie obsahu

#### Systémové príkazy
- **"ulož súbor"** - Ctrl+S
- **"kopíruj"** - Ctrl+C
- **"vlož"** - Ctrl+V
- **"vráť späť"** - Ctrl+Z

#### Aplikácie a web
- **"otvor youtube"** - Otvorí YouTube
- **"otvor google"** - Otvorí Google
- **"otvor kalkulačku"** - Spustí kalkulačku

#### Hlasitosť
- **"zvýš hlasitosť"** - Zvýši hlasitosť
- **"zníž hlasitosť"** - Zníži hlasitosť
- **"hlasitosť na 50"** - Nastaví hlasitosť na 50%

#### AI otázky
- **"Čo je Python?"** - AI odpovie na otázku
- **"Ako funguje..."** - Vysvetlenie konceptov
- **"Prečo..."** - Odpovede na otázky

### Klávesové skratky
- `A` - Automatická kalibrácia
- `R` - Reset kalibrácie
- `Q` - Ukončenie (v OpenCV okne)

## ⚙️ Konfigurácia

Hlavná konfigurácia je v súbore `config.json`:

```json
{
    "hand_tracking": {
        "max_hands": 1,
        "detection_confidence": 0.7,
        "tracking_confidence": 0.5
    },
    "cursor": {
        "smoothing": 0.5,
        "sensitivity_x": 1.3,
        "sensitivity_y": 1.3
    },
    "camera": {
        "device_id": 0,
        "width": 640,
        "height": 480
    }
}
```

### Environment Variables
- `GEMINI_API_KEY` - API kľúč pre Gemini AI (povinný)
- `DEBUG` - Zapnutie debug módu (voliteľné)
- `LOG_LEVEL` - Úroveň logovania (voliteľné)

## 🏗️ Architektúra

### Hlavné moduly

- **`main.py`** - Hlavná aplikácia a GUI
- **`air_cursor.py`** - Hand tracking a ovládanie kurzora
- **`voice_commands.py`** - Spracovanie hlasových príkazov
- **`deepseek_integration.py`** - Gemini AI integrácia
- **`config_manager.py`** - Správa konfigurácie
- **`screen_reader.py`** - OCR a webová automatizácia

### Tok dát
1. **Video vstup** → MediaPipe → Pozícia ruky → Pohyb kurzora
2. **Audio vstup** → Speech Recognition → Spracovanie príkazov → Akcie
3. **AI dotazy** → Gemini API → Text-to-Speech → Audio výstup

## 🔧 Riešenie problémov

### Časté problémy

**Kamera sa nespustí:**
- Skontrolujte `device_id` v config.json
- Uistite sa, že kamera nie je používaná inou aplikáciou

**Hlasové príkazy nefungujú:**
- Skontrolujte mikrofón v systémových nastaveniach
- Overte internetové pripojenie (Google Speech API)

**Gemini API chyby:**
- Skontrolujte API kľúč: `echo %GEMINI_API_KEY%`
- Overte kvóty na Google AI Studio

**Nepresné sledovanie ruky:**
- Spustite kalibráciu znovu (stlačte `R` potom `A`)
- Zlepšite osvetlenie
- Upravte `sensitivity_x/y` v config.json

### Debug mód
```bash
set DEBUG=true
python main.py
```

## 🤝 Prispievanie

1. Fork repository
2. Vytvorte feature branch (`git checkout -b feature/nova-funkcia`)
3. Commit zmeny (`git commit -am 'Pridaná nová funkcia'`)
4. Push do branch (`git push origin feature/nova-funkcia`)
5. Vytvorte Pull Request

## 📄 Licencia

Tento projekt je licencovaný pod MIT licenciou - pozrite súbor [LICENSE](LICENSE) pre detaily.

## 🙏 Poďakovanie

- [MediaPipe](https://mediapipe.dev/) - Hand tracking
- [Google Speech Recognition](https://cloud.google.com/speech-to-text) - Rozpoznávanie reči
- [Google Gemini](https://ai.google.dev/) - AI asistent
- [OpenCV](https://opencv.org/) - Počítačové videnie

## 📞 Podpora

Pre otázky a podporu:
- Vytvorte Issue na GitHub
- Pozrite dokumentáciu v kóde
- Skontrolujte [SECURITY_CHANGES.md](SECURITY_CHANGES.md) pre bezpečnostné informácie

---

**Vyvinuté s ❤️ pre hands-free computing**