#!/bin/bash

echo ""
echo "========================================"
echo " AirCursor Assistant - Web Interface"
echo "========================================"
echo ""

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python3 nie je nainštalovaný alebo nie je v PATH"
    echo "Nainštalujte Python3 z https://python.org"
    exit 1
fi

# Check if virtual environment exists
if [ -f "venv/bin/activate" ]; then
    echo "Aktivujem virtual environment..."
    source venv/bin/activate
else
    echo "WARNING: Virtual environment nebol nájdený"
    echo "Pokračujem s globálnym Python..."
fi

# Start web interface
echo "Spúšťam webové rozhranie..."
echo ""
python3 web_launcher.py

echo ""
echo "Webové rozhranie ukončené."
