#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON> h<PERSON> pr<PERSON>azy pre AirCursor Assistant

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>žívateľom vytvárať a spravovať vlastné hlasové príkazy.
"""

import json
import os
import re
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Callable, Any, Optional
import logging

logger = logging.getLogger(__name__)

class CustomCommand:
    """Reprezentácia vlastného hlasového príkazu."""
    
    def __init__(self, name: str, triggers: List[str], action_type: str, 
                 action_data: Dict[str, Any], description: str = ""):
        self.name = name
        self.triggers = triggers  # Zoznam fráz, ktoré spúšťajú príkaz
        self.action_type = action_type  # 'keyboard', 'mouse', 'application', 'script', 'macro'
        self.action_data = action_data  # Dáta pre akciu
        self.description = description
        self.created_at = time.time()
        self.usage_count = 0
        self.last_used = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Konvertuje príkaz na slovník."""
        return {
            'name': self.name,
            'triggers': self.triggers,
            'action_type': self.action_type,
            'action_data': self.action_data,
            'description': self.description,
            'created_at': self.created_at,
            'usage_count': self.usage_count,
            'last_used': self.last_used
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CustomCommand':
        """Vytvorí príkaz zo slovníka."""
        cmd = cls(
            data['name'],
            data['triggers'],
            data['action_type'],
            data['action_data'],
            data.get('description', '')
        )
        cmd.created_at = data.get('created_at', time.time())
        cmd.usage_count = data.get('usage_count', 0)
        cmd.last_used = data.get('last_used')
        return cmd
    
    def execute(self) -> bool:
        """Vykoná príkaz."""
        try:
            self.usage_count += 1
            self.last_used = time.time()
            
            if self.action_type == 'keyboard':
                return self._execute_keyboard()
            elif self.action_type == 'mouse':
                return self._execute_mouse()
            elif self.action_type == 'application':
                return self._execute_application()
            elif self.action_type == 'script':
                return self._execute_script()
            elif self.action_type == 'macro':
                return self._execute_macro()
            else:
                logger.error(f"Neznámy typ akcie: {self.action_type}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba pri vykonávaní príkazu {self.name}: {e}")
            return False
    
    def _execute_keyboard(self) -> bool:
        """Vykoná klávesovú akciu."""
        import pyautogui
        
        keys = self.action_data.get('keys', [])
        text = self.action_data.get('text', '')
        
        if keys:
            if len(keys) == 1:
                pyautogui.press(keys[0])
            else:
                pyautogui.hotkey(*keys)
        
        if text:
            pyautogui.write(text)
        
        return True
    
    def _execute_mouse(self) -> bool:
        """Vykoná myšovú akciu."""
        import pyautogui
        
        action = self.action_data.get('action', 'click')
        x = self.action_data.get('x')
        y = self.action_data.get('y')
        
        if action == 'click':
            if x is not None and y is not None:
                pyautogui.click(x, y)
            else:
                pyautogui.click()
        elif action == 'right_click':
            if x is not None and y is not None:
                pyautogui.rightClick(x, y)
            else:
                pyautogui.rightClick()
        elif action == 'double_click':
            if x is not None and y is not None:
                pyautogui.doubleClick(x, y)
            else:
                pyautogui.doubleClick()
        elif action == 'scroll':
            direction = self.action_data.get('direction', 'up')
            amount = self.action_data.get('amount', 3)
            pyautogui.scroll(amount if direction == 'up' else -amount)
        
        return True
    
    def _execute_application(self) -> bool:
        """Spustí aplikáciu."""
        app_path = self.action_data.get('path', '')
        args = self.action_data.get('args', [])
        
        if not app_path:
            return False
        
        try:
            if args:
                subprocess.Popen([app_path] + args)
            else:
                subprocess.Popen(app_path, shell=True)
            return True
        except Exception as e:
            logger.error(f"Chyba pri spúšťaní aplikácie {app_path}: {e}")
            return False
    
    def _execute_script(self) -> bool:
        """Vykoná script."""
        script_path = self.action_data.get('path', '')
        interpreter = self.action_data.get('interpreter', 'python')
        args = self.action_data.get('args', [])
        
        if not script_path or not os.path.exists(script_path):
            return False
        
        try:
            cmd = [interpreter, script_path] + args
            subprocess.Popen(cmd)
            return True
        except Exception as e:
            logger.error(f"Chyba pri vykonávaní scriptu {script_path}: {e}")
            return False
    
    def _execute_macro(self) -> bool:
        """Vykoná makro (sériu akcií)."""
        actions = self.action_data.get('actions', [])
        delay = self.action_data.get('delay', 0.1)
        
        for action in actions:
            if action['type'] == 'keyboard':
                self._execute_keyboard_action(action)
            elif action['type'] == 'mouse':
                self._execute_mouse_action(action)
            elif action['type'] == 'wait':
                time.sleep(action.get('duration', 1.0))
            
            time.sleep(delay)
        
        return True
    
    def _execute_keyboard_action(self, action: Dict[str, Any]):
        """Pomocná metóda pre klávesové akcie v makre."""
        import pyautogui
        
        if 'keys' in action:
            pyautogui.hotkey(*action['keys'])
        elif 'text' in action:
            pyautogui.write(action['text'])
    
    def _execute_mouse_action(self, action: Dict[str, Any]):
        """Pomocná metóda pre myšové akcie v makre."""
        import pyautogui
        
        if action.get('action') == 'click':
            pyautogui.click(action.get('x'), action.get('y'))
        elif action.get('action') == 'move':
            pyautogui.moveTo(action.get('x'), action.get('y'))

class CustomVoiceCommandManager:
    """Správca vlastných hlasových príkazov."""
    
    def __init__(self, commands_file: str = "custom_commands.json"):
        self.commands_file = Path(commands_file)
        self.commands: Dict[str, CustomCommand] = {}
        self.load_commands()
    
    def load_commands(self):
        """Načíta príkazy zo súboru."""
        if not self.commands_file.exists():
            logger.info("Súbor s vlastnými príkazmi neexistuje, vytváram nový.")
            self.save_commands()
            return
        
        try:
            with open(self.commands_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.commands = {}
            for cmd_data in data.get('commands', []):
                cmd = CustomCommand.from_dict(cmd_data)
                self.commands[cmd.name] = cmd
            
            logger.info(f"Načítaných {len(self.commands)} vlastných príkazov.")
            
        except Exception as e:
            logger.error(f"Chyba pri načítaní príkazov: {e}")
            self.commands = {}
    
    def save_commands(self):
        """Uloží príkazy do súboru."""
        try:
            data = {
                'version': '1.0',
                'commands': [cmd.to_dict() for cmd in self.commands.values()]
            }
            
            with open(self.commands_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Uložených {len(self.commands)} vlastných príkazov.")
            
        except Exception as e:
            logger.error(f"Chyba pri ukladaní príkazov: {e}")
    
    def add_command(self, command: CustomCommand) -> bool:
        """Pridá nový príkaz."""
        if command.name in self.commands:
            logger.warning(f"Príkaz {command.name} už existuje.")
            return False
        
        self.commands[command.name] = command
        self.save_commands()
        logger.info(f"Pridaný nový príkaz: {command.name}")
        return True
    
    def remove_command(self, name: str) -> bool:
        """Odstráni príkaz."""
        if name not in self.commands:
            return False
        
        del self.commands[name]
        self.save_commands()
        logger.info(f"Odstránený príkaz: {name}")
        return True
    
    def update_command(self, name: str, command: CustomCommand) -> bool:
        """Aktualizuje existujúci príkaz."""
        if name not in self.commands:
            return False
        
        # Zachovanie štatistík
        old_cmd = self.commands[name]
        command.usage_count = old_cmd.usage_count
        command.last_used = old_cmd.last_used
        command.created_at = old_cmd.created_at
        
        self.commands[name] = command
        self.save_commands()
        logger.info(f"Aktualizovaný príkaz: {name}")
        return True
    
    def find_command_by_trigger(self, text: str) -> Optional[CustomCommand]:
        """Nájde príkaz na základe triggeru."""
        text_lower = text.lower().strip()
        
        for command in self.commands.values():
            for trigger in command.triggers:
                if trigger.lower() in text_lower:
                    return command
        
        return None
    
    def get_command(self, name: str) -> Optional[CustomCommand]:
        """Vráti príkaz podľa mena."""
        return self.commands.get(name)
    
    def list_commands(self) -> List[CustomCommand]:
        """Vráti zoznam všetkých príkazov."""
        return list(self.commands.values())
    
    def get_statistics(self) -> Dict[str, Any]:
        """Vráti štatistiky použitia príkazov."""
        total_commands = len(self.commands)
        total_usage = sum(cmd.usage_count for cmd in self.commands.values())
        
        most_used = None
        if self.commands:
            most_used = max(self.commands.values(), key=lambda x: x.usage_count)
        
        return {
            'total_commands': total_commands,
            'total_usage': total_usage,
            'most_used': most_used.name if most_used else None,
            'most_used_count': most_used.usage_count if most_used else 0
        }

def create_sample_commands() -> List[CustomCommand]:
    """Vytvorí ukážkové príkazy."""
    commands = []
    
    # Príkaz na otvorenie Notepadu
    commands.append(CustomCommand(
        name="open_notepad",
        triggers=["otvor poznámky", "spusti notepad", "nový dokument"],
        action_type="application",
        action_data={"path": "notepad.exe"},
        description="Otvorí Notepad"
    ))
    
    # Príkaz na uloženie súboru
    commands.append(CustomCommand(
        name="save_file",
        triggers=["ulož súbor", "save", "uložiť"],
        action_type="keyboard",
        action_data={"keys": ["ctrl", "s"]},
        description="Uloží aktuálny súbor"
    ))
    
    # Príkaz na napísanie emailu
    commands.append(CustomCommand(
        name="write_email",
        triggers=["napíš email", "nový email"],
        action_type="keyboard",
        action_data={"text": "Dobrý deň,\n\n\n\nS pozdravom,\n"},
        description="Napíše šablónu emailu"
    ))
    
    # Makro príkaz
    commands.append(CustomCommand(
        name="screenshot_macro",
        triggers=["urob screenshot", "snímka obrazovky"],
        action_type="macro",
        action_data={
            "actions": [
                {"type": "keyboard", "keys": ["win", "shift", "s"]},
                {"type": "wait", "duration": 0.5}
            ],
            "delay": 0.1
        },
        description="Urobí screenshot pomocou Windows nástroja"
    ))
    
    return commands

if __name__ == "__main__":
    # Test vlastných príkazov
    manager = CustomVoiceCommandManager("test_commands.json")
    
    # Pridanie ukážkových príkazov
    sample_commands = create_sample_commands()
    for cmd in sample_commands:
        manager.add_command(cmd)
    
    # Test vyhľadávania
    found = manager.find_command_by_trigger("otvor poznámky")
    if found:
        print(f"Nájdený príkaz: {found.name}")
        found.execute()
    
    # Štatistiky
    stats = manager.get_statistics()
    print(f"Štatistiky: {stats}")
