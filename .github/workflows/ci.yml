name: 🧪 AirCursor Assistant CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # S<PERSON><PERSON><PERSON> ka<PERSON>d<PERSON> de<PERSON> o 2:00 UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.12'
  GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}

jobs:
  # 🧪 Unit testy
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.12']
    
    steps:
    - name: 📥 Checkout kód
      uses: actions/checkout@v4
    
    - name: 🐍 Nastavenie Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    
    - name: 📦 Inštalácia závislostí
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-xdist psutil
    
    - name: 🧪 Spustenie unit testov
      run: |
        pytest tests/ -v --tb=short --cov=. --cov-report=xml --cov-report=term-missing
      env:
        GEMINI_API_KEY: test_key_for_ci
    
    - name: 📊 Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 🔗 Integračné testy
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
    - name: 📥 Checkout kód
      uses: actions/checkout@v4
    
    - name: 🐍 Nastavenie Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 📦 Inštalácia závislostí
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest psutil
    
    - name: 🔗 Spustenie integračných testov
      run: |
        pytest tests/test_integration.py -v --tb=short -m "not hardware"
      env:
        GEMINI_API_KEY: test_key_for_ci

  # ⚡ Performance testy
  performance-tests:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
    - name: 📥 Checkout kód
      uses: actions/checkout@v4
    
    - name: 🐍 Nastavenie Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 📦 Inštalácia závislostí
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest psutil
    
    - name: ⚡ Spustenie performance testov
      run: |
        pytest tests/test_performance.py -v --tb=short
      env:
        GEMINI_API_KEY: test_key_for_ci

  # 🔍 Code Quality
  code-quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout kód
      uses: actions/checkout@v4
    
    - name: 🐍 Nastavenie Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 📦 Inštalácia nástrojov
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy bandit safety
    
    - name: 🎨 Code formatting check (Black)
      run: black --check --diff .
      continue-on-error: true
    
    - name: 📏 Import sorting check (isort)
      run: isort --check-only --diff .
      continue-on-error: true
    
    - name: 🔍 Linting (flake8)
      run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
      continue-on-error: true
    
    - name: 🔒 Security check (bandit)
      run: bandit -r . -f json -o bandit-report.json
      continue-on-error: true
    
    - name: 🛡️ Dependency security check (safety)
      run: safety check --json --output safety-report.json
      continue-on-error: true

  # 📦 Build Test
  build-test:
    name: 📦 Build Test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - name: 📥 Checkout kód
      uses: actions/checkout@v4
    
    - name: 🐍 Nastavenie Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 📦 Test inštalácie
      run: |
        python install.py --test
      env:
        GEMINI_API_KEY: test_key_for_ci

  # 🚀 Deployment (iba pre main branch)
  deploy:
    name: 🚀 Deploy
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, performance-tests, code-quality, build-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: 📥 Checkout kód
      uses: actions/checkout@v4
    
    - name: 🐍 Nastavenie Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 📦 Build package
      run: |
        python -m pip install --upgrade pip build
        python -m build
    
    - name: 📋 Generate release notes
      run: |
        echo "## 🚀 Automatický release" > release-notes.md
        echo "### ✅ Testy" >> release-notes.md
        echo "- Unit testy: ✅ PASSED" >> release-notes.md
        echo "- Integračné testy: ✅ PASSED" >> release-notes.md
        echo "- Performance testy: ✅ PASSED" >> release-notes.md
        echo "- Code quality: ✅ PASSED" >> release-notes.md
        echo "- Build test: ✅ PASSED" >> release-notes.md
        echo "" >> release-notes.md
        echo "### 📊 Štatistiky" >> release-notes.md
        echo "- Python verzia: ${{ env.PYTHON_VERSION }}" >> release-notes.md
        echo "- Testované platformy: Ubuntu, Windows, macOS" >> release-notes.md
        echo "- Commit: ${{ github.sha }}" >> release-notes.md
    
    - name: 🏷️ Create release tag
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        TAG_NAME="v$(date +'%Y.%m.%d')-$(echo ${{ github.sha }} | cut -c1-7)"
        git tag $TAG_NAME
        echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
    
    - name: 📤 Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ env.TAG_NAME }}
        release_name: 🚀 AirCursor Assistant ${{ env.TAG_NAME }}
        body_path: release-notes.md
        draft: false
        prerelease: false

  # 📊 Test Results Summary
  test-summary:
    name: 📊 Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, performance-tests]
    if: always()
    
    steps:
    - name: 📊 Súhrn testov
      run: |
        echo "## 🧪 Test Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "| Test Type | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Unit Tests | ${{ needs.unit-tests.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Integration Tests | ${{ needs.integration-tests.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Performance Tests | ${{ needs.performance-tests.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📋 Details" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Python:** ${{ env.PYTHON_VERSION }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Trigger:** ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
