[flake8]
# Flake8 konfigur<PERSON><PERSON> pre AirCursor Assistant

# <PERSON><PERSON>ln<PERSON> dĺžka riadku
max-line-length = 100

# Ignorované chyby a varovania
ignore = 
    # E203: whitespace before ':'
    E203,
    # W503: line break before binary operator
    W503,
    # E501: line too long (handled by black)
    E501,
    # F401: imported but unused (môže byť užitočné pre __init__.py)
    F401

# Súbory na ignorovanie
exclude = 
    .git,
    __pycache__,
    .pytest_cache,
    .venv,
    venv,
    build,
    dist,
    *.egg-info,
    .tox,
    htmlcov,
    .coverage

# Adresáre na kontrolu
include = 
    *.py

# Maximálna komplexnosť McCabe
max-complexity = 10

# Počet chýb na zobrazenie
count = True

# Zobrazenie štatistík
statistics = True

# Formát výstupu
format = %(path)s:%(row)d:%(col)d: %(code)s %(text)s

# Per-file ignores
per-file-ignores =
    # __init__.py súbory môžu mať nepoužité importy
    __init__.py:F401,F403
    # Test súbory môžu mať dlhšie riadky a iné štýly
    test_*.py:E501,F401,F811
    tests/*.py:E501,F401,F811
    # Setup súbory
    setup.py:E501
    install.py:E501

# Docstring konvencie
docstring-convention = google

# Import order checking (ak je nainštalovaný flake8-import-order)
import-order-style = google
application-import-names = air_cursor,voice_commands,deepseek_integration,config_manager,screen_reader

# Naming conventions
inline-quotes = double
multiline-quotes = double

# Security
# Ak je nainštalovaný flake8-bandit
bandit-config = .bandit

# Builtins (ak je nainštalovaný flake8-builtins)
builtins = _

# Comprehensions (ak je nainštalovaný flake8-comprehensions)
# Žiadne špeciálne nastavenia

# Logging (ak je nainštalovaný flake8-logging-format)
logging-format-style = %

# Variables (ak je nainštalovaný flake8-variables-names)
# Žiadne špeciálne nastavenia
