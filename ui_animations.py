#!/usr/bin/env python3
"""
UI Animácie a prechody pre AirCursor Assistant

<PERSON><PERSON><PERSON><PERSON> plyn<PERSON> anim<PERSON><PERSON> pre GUI komponenty.
"""

import tkinter as tk
import math
import time
import threading
from typing import Callable, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class AnimationManager:
    """Správca animácií pre GUI komponenty."""
    
    def __init__(self):
        self.active_animations = {}
        self.animation_id_counter = 0
        
    def animate_fade(self, widget: tk.Widget, start_alpha: float = 0.0, 
                    end_alpha: float = 1.0, duration: float = 0.5,
                    callback: Optional[Callable] = None) -> int:
        """Anim<PERSON>cia fade in/out pre widget."""
        animation_id = self._get_next_id()
        
        def fade_step(current_time: float, start_time: float):
            if animation_id not in self.active_animations:
                return
                
            elapsed = current_time - start_time
            progress = min(elapsed / duration, 1.0)
            
            # Easing function (ease-out)
            eased_progress = 1 - (1 - progress) ** 3
            
            current_alpha = start_alpha + (end_alpha - start_alpha) * eased_progress
            
            try:
                # Simulácia alpha cez farbu pozadia
                if hasattr(widget, 'configure'):
                    if current_alpha < 0.3:
                        widget.configure(state='disabled')
                    else:
                        widget.configure(state='normal')
                        
                if progress >= 1.0:
                    self._finish_animation(animation_id)
                    if callback:
                        callback()
                else:
                    widget.after(16, lambda: fade_step(time.time(), start_time))
                    
            except tk.TclError:
                self._finish_animation(animation_id)
        
        self.active_animations[animation_id] = True
        fade_step(time.time(), time.time())
        return animation_id
    
    def animate_slide(self, widget: tk.Widget, start_x: int, end_x: int,
                     start_y: int, end_y: int, duration: float = 0.3,
                     callback: Optional[Callable] = None) -> int:
        """Animácia posunu widgetu."""
        animation_id = self._get_next_id()
        
        def slide_step(current_time: float, start_time: float):
            if animation_id not in self.active_animations:
                return
                
            elapsed = current_time - start_time
            progress = min(elapsed / duration, 1.0)
            
            # Easing function (ease-in-out)
            eased_progress = 0.5 * (1 - math.cos(progress * math.pi))
            
            current_x = int(start_x + (end_x - start_x) * eased_progress)
            current_y = int(start_y + (end_y - start_y) * eased_progress)
            
            try:
                widget.place(x=current_x, y=current_y)
                
                if progress >= 1.0:
                    self._finish_animation(animation_id)
                    if callback:
                        callback()
                else:
                    widget.after(16, lambda: slide_step(time.time(), start_time))
                    
            except tk.TclError:
                self._finish_animation(animation_id)
        
        self.active_animations[animation_id] = True
        slide_step(time.time(), time.time())
        return animation_id
    
    def animate_scale(self, widget: tk.Widget, start_scale: float = 0.0,
                     end_scale: float = 1.0, duration: float = 0.4,
                     callback: Optional[Callable] = None) -> int:
        """Animácia zväčšenia/zmenšenia widgetu."""
        animation_id = self._get_next_id()
        
        # Uloženie pôvodných rozmerov
        original_width = widget.winfo_reqwidth()
        original_height = widget.winfo_reqheight()
        
        def scale_step(current_time: float, start_time: float):
            if animation_id not in self.active_animations:
                return
                
            elapsed = current_time - start_time
            progress = min(elapsed / duration, 1.0)
            
            # Easing function (bounce)
            if progress < 0.5:
                eased_progress = 2 * progress * progress
            else:
                eased_progress = 1 - 2 * (1 - progress) * (1 - progress)
            
            current_scale = start_scale + (end_scale - start_scale) * eased_progress
            
            try:
                new_width = int(original_width * current_scale)
                new_height = int(original_height * current_scale)
                
                if hasattr(widget, 'configure'):
                    widget.configure(width=max(1, new_width), height=max(1, new_height))
                
                if progress >= 1.0:
                    self._finish_animation(animation_id)
                    if callback:
                        callback()
                else:
                    widget.after(16, lambda: scale_step(time.time(), start_time))
                    
            except tk.TclError:
                self._finish_animation(animation_id)
        
        self.active_animations[animation_id] = True
        scale_step(time.time(), time.time())
        return animation_id
    
    def animate_color(self, widget: tk.Widget, start_color: str, end_color: str,
                     duration: float = 0.5, callback: Optional[Callable] = None) -> int:
        """Animácia zmeny farby widgetu."""
        animation_id = self._get_next_id()
        
        # Konverzia hex farieb na RGB
        start_rgb = self._hex_to_rgb(start_color)
        end_rgb = self._hex_to_rgb(end_color)
        
        def color_step(current_time: float, start_time: float):
            if animation_id not in self.active_animations:
                return
                
            elapsed = current_time - start_time
            progress = min(elapsed / duration, 1.0)
            
            # Linear interpolation
            current_rgb = [
                int(start_rgb[i] + (end_rgb[i] - start_rgb[i]) * progress)
                for i in range(3)
            ]
            
            current_color = self._rgb_to_hex(current_rgb)
            
            try:
                if hasattr(widget, 'configure'):
                    widget.configure(bg=current_color)
                
                if progress >= 1.0:
                    self._finish_animation(animation_id)
                    if callback:
                        callback()
                else:
                    widget.after(16, lambda: color_step(time.time(), start_time))
                    
            except tk.TclError:
                self._finish_animation(animation_id)
        
        self.active_animations[animation_id] = True
        color_step(time.time(), time.time())
        return animation_id
    
    def animate_pulse(self, widget: tk.Widget, duration: float = 1.0,
                     cycles: int = 3, callback: Optional[Callable] = None) -> int:
        """Animácia pulzovania widgetu."""
        animation_id = self._get_next_id()
        
        def pulse_step(current_time: float, start_time: float):
            if animation_id not in self.active_animations:
                return
                
            elapsed = current_time - start_time
            total_duration = duration * cycles
            progress = min(elapsed / total_duration, 1.0)
            
            # Sínusová funkcia pre pulsovanie
            pulse_value = (math.sin(elapsed * 2 * math.pi / duration) + 1) / 2
            
            try:
                # Zmena priehľadnosti cez relief
                if pulse_value > 0.5:
                    widget.configure(relief='raised')
                else:
                    widget.configure(relief='sunken')
                
                if progress >= 1.0:
                    widget.configure(relief='flat')
                    self._finish_animation(animation_id)
                    if callback:
                        callback()
                else:
                    widget.after(50, lambda: pulse_step(time.time(), start_time))
                    
            except tk.TclError:
                self._finish_animation(animation_id)
        
        self.active_animations[animation_id] = True
        pulse_step(time.time(), time.time())
        return animation_id
    
    def stop_animation(self, animation_id: int):
        """Zastaví konkrétnu animáciu."""
        if animation_id in self.active_animations:
            del self.active_animations[animation_id]
    
    def stop_all_animations(self):
        """Zastaví všetky aktívne animácie."""
        self.active_animations.clear()
    
    def _get_next_id(self) -> int:
        """Vráti ďalšie ID pre animáciu."""
        self.animation_id_counter += 1
        return self.animation_id_counter
    
    def _finish_animation(self, animation_id: int):
        """Dokončí animáciu."""
        if animation_id in self.active_animations:
            del self.active_animations[animation_id]
    
    def _hex_to_rgb(self, hex_color: str) -> tuple:
        """Konvertuje hex farbu na RGB."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def _rgb_to_hex(self, rgb: list) -> str:
        """Konvertuje RGB na hex farbu."""
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

class AnimatedWidget:
    """Wrapper pre widget s animačnými schopnosťami."""
    
    def __init__(self, widget: tk.Widget, animation_manager: AnimationManager):
        self.widget = widget
        self.animation_manager = animation_manager
        self.current_animations = []
    
    def fade_in(self, duration: float = 0.5, callback: Optional[Callable] = None):
        """Fade in animácia."""
        anim_id = self.animation_manager.animate_fade(
            self.widget, 0.0, 1.0, duration, callback
        )
        self.current_animations.append(anim_id)
        return anim_id
    
    def fade_out(self, duration: float = 0.5, callback: Optional[Callable] = None):
        """Fade out animácia."""
        anim_id = self.animation_manager.animate_fade(
            self.widget, 1.0, 0.0, duration, callback
        )
        self.current_animations.append(anim_id)
        return anim_id
    
    def slide_in_from_left(self, distance: int = 100, duration: float = 0.3,
                          callback: Optional[Callable] = None):
        """Slide in z ľava."""
        current_x = self.widget.winfo_x()
        current_y = self.widget.winfo_y()
        start_x = current_x - distance
        
        self.widget.place(x=start_x, y=current_y)
        
        anim_id = self.animation_manager.animate_slide(
            self.widget, start_x, current_x, current_y, current_y, duration, callback
        )
        self.current_animations.append(anim_id)
        return anim_id
    
    def pulse(self, duration: float = 1.0, cycles: int = 3,
             callback: Optional[Callable] = None):
        """Pulsovanie widgetu."""
        anim_id = self.animation_manager.animate_pulse(
            self.widget, duration, cycles, callback
        )
        self.current_animations.append(anim_id)
        return anim_id
    
    def stop_all_animations(self):
        """Zastaví všetky animácie tohto widgetu."""
        for anim_id in self.current_animations:
            self.animation_manager.stop_animation(anim_id)
        self.current_animations.clear()

# Globálny animation manager
_global_animation_manager = AnimationManager()

def get_animation_manager() -> AnimationManager:
    """Vráti globálny animation manager."""
    return _global_animation_manager

def animate_widget(widget: tk.Widget) -> AnimatedWidget:
    """Vytvorí AnimatedWidget wrapper."""
    return AnimatedWidget(widget, get_animation_manager())

if __name__ == "__main__":
    # Test animácií
    root = tk.Tk()
    root.title("Test animácií")
    root.geometry("400x300")
    
    # Test button
    button = tk.Button(root, text="Test Button", bg="lightblue")
    button.place(x=150, y=100)
    
    animated_button = animate_widget(button)
    
    def test_animations():
        animated_button.fade_in(0.5)
        root.after(1000, lambda: animated_button.pulse(1.0, 2))
        root.after(3000, lambda: animated_button.slide_in_from_left(100, 0.5))
    
    root.after(500, test_animations)
    root.mainloop()
