#!/usr/bin/env python3
"""
Jednoduchý web launcher pre AirCursor Assistant (bez Unicode znakov)
"""

import sys
import time
import logging
from pathlib import Path

# Pridanie project root do Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from web_interface import WebInterface
    from custom_voice_commands import CustomVoiceCommandManager
    from plugin_system import PluginManager
    WEB_INTERFACE_AVAILABLE = True
except ImportError as e:
    print(f"Import error: {e}")
    WEB_INTERFACE_AVAILABLE = False

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def main():
    """Hlavna funkcia web launchera."""
    print("AirCursor Assistant - Web Interface")
    print("=" * 40)
    
    if not WEB_INTERFACE_AVAILABLE:
        print("ERROR: Web interface is not available!")
        print("Please install dependencies: pip install flask")
        return 1
    
    try:
        print("Initializing managers...")
        custom_commands_manager = CustomVoiceCommandManager()
        plugin_manager = PluginManager()
        
        plugin_manager.discover_plugins()
        
        print("Initializing web interface...")
        web_interface = WebInterface(
            config_manager=None,
            plugin_manager=plugin_manager,
            custom_commands_manager=custom_commands_manager,
            port=8080
        )
        
        print("Starting web server on port 8080...")
        web_interface.start(open_browser=True)
        
        print("SUCCESS: Web interface is running!")
        print("Open browser at: http://localhost:8080")
        print("Press Ctrl+C to stop")
        print("=" * 40)
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nStopping web server...")
            web_interface.stop()
            print("Server stopped successfully.")
            
    except Exception as e:
        logger.error(f"Error starting web interface: {e}")
        print(f"ERROR: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
